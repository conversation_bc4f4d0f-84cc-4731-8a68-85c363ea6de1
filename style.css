/*
 Theme Name:   GP Discover Pro
 Theme URI:    https://wplitetheme.com/gp-discover-pro/
 Description:  GeneratePress Child theme for mobile responsive discovers blog and news website. Using this child theme you can easily create a google discover blog website. This GeneratePress Child theme design by wplitetheme.com
 Author:       WPLiteTheme.com
 Author URI:   https://wplitetheme.com
 Template:     generatepress
 Version:      1.4.0
*/

.main-navigation {
	box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
}
/* Sticky widgets */ 
.auto-width.gb-query-loop-wrapper {
    flex: 1;
}
@media (min-width: 768px) {
.sticky-container > .gb-inside-container,.sticky-container {
    position: sticky;
    top: 80px;
}
#right-sidebar .inside-right-sidebar {
    height: 100%;
}
}
select#wp-block-categories-1 {
    width: 100%;
}
.wplite-banner-ads {
	margin-bottom: 15px;
}
.rank-math-breadcrumb p {
	background: #e5e8ec;
	color: #000000;
	padding: 5px 10px;
	border-radius: 4px;
    font-size: 11px;
	font-weight: 700;
}
@media (max-width: 768px){
	.rank-math-breadcrumb p {
		margin: 0px 10px;
	}
}
/*Block Images*/
.wp-block-image {
    padding-top: 10px;
    padding-bottom: 20px;
}

.wp-block-image img {
    box-shadow: 0 10px 10px 0 rgb(0 0 0 / 6%);
    border: 1px solid #cfcfcf;
    padding: 3px;
}
/*Table of contents*/
#toc_container li a {
    display: block;
    width: 100%;
    color: var(--link-text);
    padding: 10px 1em;
    border-top: 1px solid #aaa;
}
#toc_container {
    background: #f9f9f900;
    border: 1px solid #aaa;
    padding: 0px;
}
#toc_container span.toc_toggle {
    font-weight: 400;
    background: #fff;
    padding: 3px 20px;
    font-size: 18px;
    text-transform: capitalize;
    text-align: center;
    display: block;
}
/*Floating Social Share*/
.wplitetheme-float-social-wrapper {
    position: fixed;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    z-index: 9999;
}

.wplitetheme-float-social-sharing {
    display: flex;
    flex-wrap: nowrap;
    flex-direction: column;
    align-items: flex-start;
    min-height: 40px;
    font-size: 12px;
    padding: 5px 10px;
}

.wplitetheme-float-social-sharing svg {
    position: relative;
    top: 0.5em;
}

.wplitetheme-social-facebook {
    fill: #fff;
    background-color: rgba(59, 89, 152, 1);
}

.wplitetheme-social-facebook:hover {
    background-color: rgba(59, 89, 152, .8);
}

.wplitetheme-social-twitter {
    fill: #fff;
    background-color: #0f1419;
}

.wplitetheme-social-twitter:hover {
    background-color: #0f1419;
}

.wplitetheme-social-whatsapp {
    fill: #fff;
    background-color: rgba(37, 211, 102, 1);
}

.wplitetheme-social-whatsapp:hover {
    background-color: rgba(37, 211, 102, .8);
}

/*Social Share*/
.wpjankari-social-wrapper {
    margin: 20px 5px 20px 5px;
    font-size: 0;
    text-align: center;
	display: flex;
    flex-wrap: wrap;
}
.wpjankari-social-sharing {
	padding: 8px;
	margin: 4px;
	border-radius: 3px;
    flex: 1;
    transition: background-color 0.3s, transform 0.3s, color 0.3s;
}
.wpjankari-social-sharing:hover {
	transform: translateY(-3px);
	border: none;
	box-shadow: rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px;
}
@media only screen and (max-width: 600px) {
    .wpjankari-social-sharing {
		display: inline-block;
    }
}
.wpjankari-social-sharing svg {
    position: relative;
    top: 0.15em;
    display: inline-block;
}
.wpjankari-social-facebook {
    fill: #fff;
    background-color: rgba(59, 89, 152, 1);
}
.wpjankari-social-twitter {
    fill: #fff;
    background-color: rgba(0, 0, 0);
}
.wpjankari-social-whatsapp {
    fill: #fff;
    background-color: rgba(37, 211, 102, 1);
}
.wpjankari-social-telegram {
    fill: #fff;
    background-color: rgb(2, 126, 189);
}
.wpjankari-social-more {
    fill: #fff;
    background-color: rgba(0, 0, 0);
}
/*Follow Us Button*/
.whatsapphighlight {
	animation: WPLiteTheme-GreenBorderAnimation 1s infinite;
}
@keyframes WPLiteTheme-GreenBorderAnimation {
	0% {
		border-color: transparent;
	}
	50% {
		border-color: #25d366;
	}
	100% {
		border-color: transparent;
	}
}
.telegramhighlight {
	animation: WPLiteTheme-BlueBorderAnimation 1s infinite;
}
@keyframes WPLiteTheme-BlueBorderAnimation {
	0% {
		border-color: transparent;
	}
	50% {
		border-color: #0086ce;
	}
	100% {
		border-color: transparent;
	}
}
/*Tag Cloud*/
.widget_epcl_tag_cloud a, .widget_tag_cloud a, .wp-block-tag-cloud a {
    font-size: 12px!important;
    margin-bottom: 8px;
    margin-right: 8px;
}
.widget_epcl_tag_cloud a, .widget_tag_cloud a, .wp-block-tag-cloud a,  div.tags a{
    color: #333333;
    display: inline-block;
    padding: 4px 15px;
    line-height: 1.2;
    margin-right: 10px;
    background: #fff;
    border: 1px solid #333333;
    border-radius: 25px;
}