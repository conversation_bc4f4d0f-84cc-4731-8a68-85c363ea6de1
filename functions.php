<?php
/**
 * GeneratePress child theme functions and definitions.
 *
 * Add your custom PHP in this file.
 * Only edit this file if you have direct access to it on your server (to fix errors if they happen).
 */

add_shortcode('search_title', 'get_search_title');
function get_search_title() {
    if (is_search()) {
        return '<h1 class="search-for">Search results for:</h1><h1 class="search-title">' . get_search_query() . '</h1>';
    } elseif (is_archive()) {
        return '<h1 class="search-title">' . get_the_archive_title() . '</h1>';
    } 
}